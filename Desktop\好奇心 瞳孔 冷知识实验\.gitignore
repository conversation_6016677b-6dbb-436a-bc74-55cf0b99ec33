# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# 实验数据文件
data/raw/*.edf
data/raw/*.asc
data/raw/*.csv
data/processed/*.csv
data/processed/*.pkl
data/results/*.png
data/results/*.pdf

# 日志文件
logs/*.log
logs/*.txt

# 临时文件
*.tmp
*.temp
*~

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# PsychoPy特定文件
*.psydat
*.log
lastrun.py

# EyeLink文件
*.edf
*.asc

# 配置文件（包含敏感信息）
config/local_config.py
config/secret_config.py
