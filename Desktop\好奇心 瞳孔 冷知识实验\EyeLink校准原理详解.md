# EyeLink眼动仪校准原理详解

## 🔬 EyeLink工作原理

### 基本检测技术
EyeLink眼动仪使用**瞳孔-角膜反射技术**（Pupil-Corneal Reflection, PCR）：

1. **红外光源**：发射红外光照射眼球
2. **瞳孔检测**：识别瞳孔的中心位置
3. **角膜反射**：检测角膜表面的红外光反射点（第一普尔金斯像）
4. **相对位置计算**：计算瞳孔中心与角膜反射点的相对位置

### 为什么需要两个特征点？
- **瞳孔中心**：随眼球转动而移动，但受头部移动影响
- **角膜反射**：相对固定，用于补偿头部移动
- **相对位置**：两者的相对位置与注视方向成正比关系

## 🎯 校准的目的和必要性

### 1. 建立映射关系
**核心目标**：建立眼球特征与屏幕坐标的数学映射模型

```
眼球特征 (瞳孔-角膜反射向量) → 屏幕坐标 (X, Y像素位置)
```

### 2. 个体差异补偿
每个人的眼球特征不同：
- **眼球大小**：影响瞳孔-角膜反射的基准距离
- **角膜曲率**：影响反射光的位置
- **瞳孔形状**：影响瞳孔中心的计算
- **眼球位置**：在眼眶中的相对位置

### 3. 设备参数校正
- **摄像头位置**：相对于被试眼球的位置
- **光源强度**：确保最佳的图像对比度
- **检测阈值**：优化瞳孔和反射点的识别

## 📐 校准过程详解

### 标准9点校准
```
1 ---- 2 ---- 3
|      |      |
4 ---- 5 ---- 6
|      |      |
7 ---- 8 ---- 9
```

**校准流程**：
1. **呈现校准点**：在屏幕特定位置显示校准目标
2. **被试注视**：被试注视校准点直到稳定
3. **数据采集**：记录此时的瞳孔-角膜反射特征
4. **重复采样**：每个点采集多次数据确保稳定性
5. **建立模型**：使用多项式回归建立映射关系

### 5点校准（简化版）
```
1 ---- 2 ---- 3
|      |      |
|      4      |
|      |      |
5 ---- 6 ---- 7
```
仅使用5个关键点，适用于快速校准。

## 🔍 校准什么？

### 1. 注视点位置（主要）
- **X坐标映射**：水平眼动 → 屏幕X坐标
- **Y坐标映射**：垂直眼动 → 屏幕Y坐标
- **非线性校正**：补偿眼球运动的非线性特性

### 2. 瞳孔大小（次要）
- **基准瞳孔大小**：建立瞳孔大小的参考值
- **光照补偿**：不同光照条件下的瞳孔大小变化
- **个体基线**：每个人的瞳孔大小基线不同

### 3. 系统参数
- **采样率校准**：确保1000Hz的准确采样
- **延迟校准**：补偿系统处理延迟
- **噪声水平**：确定数据质量阈值

## ⚖️ 验证过程

### 验证的目的
校准完成后需要验证精度：
- **精度测试**：测量校准误差
- **稳定性测试**：检查校准的一致性
- **覆盖范围测试**：确保整个屏幕区域的准确性

### 验证指标
- **平均误差**：< 0.5°（优秀）, < 1.0°（可接受）
- **最大误差**：< 1.0°（优秀）, < 2.0°（可接受）
- **RMS误差**：均方根误差，综合精度指标

## 🔧 校准质量影响因素

### 被试相关因素
1. **眼镜/隐形眼镜**：可能影响反射特性
2. **眼部化妆**：睫毛膏、眼影可能干扰检测
3. **眼部疾病**：白内障、角膜病变等
4. **注意力集中度**：是否能稳定注视校准点

### 环境因素
1. **光照条件**：过强或过弱都会影响检测
2. **屏幕反光**：可能产生干扰反射
3. **头部稳定性**：头托的使用很重要
4. **距离稳定性**：与眼动仪的距离要固定

### 设备因素
1. **摄像头清洁度**：镜头污染影响图像质量
2. **红外光源稳定性**：光源强度要稳定
3. **机械稳定性**：设备不能有振动
4. **软件版本**：算法的优化程度

## 📊 校准数据的数学模型

### 基本映射函数
```python
# 二次多项式映射模型
screen_x = a0 + a1*px + a2*py + a3*px*py + a4*px² + a5*py²
screen_y = b0 + b1*px + b2*py + b3*px*py + b4*px² + b5*py²

# 其中：
# px, py = 瞳孔-角膜反射的相对位置
# screen_x, screen_y = 屏幕坐标
# a0-a5, b0-b5 = 校准得到的系数
```

### 校准质量评估
```python
# 计算校准误差
def calculate_error(true_pos, predicted_pos):
    dx = true_pos[0] - predicted_pos[0]
    dy = true_pos[1] - predicted_pos[1]
    error = sqrt(dx² + dy²)
    return error

# 角度误差转换
visual_angle = arctan(pixel_error / viewing_distance) * 180 / π
```

## 🎯 实际应用建议

### 校准前准备
1. **调整座椅高度**：眼睛与屏幕中心等高
2. **设置头托**：保持头部稳定
3. **调整距离**：通常60-70cm
4. **检查光照**：避免强光直射

### 校准过程
1. **解释流程**：告知被试校准的重要性
2. **练习注视**：先练习稳定注视
3. **逐点校准**：按顺序完成所有校准点
4. **验证检查**：确认校准质量

### 校准后维护
1. **定期重校准**：长时间实验需要重新校准
2. **漂移校正**：实验过程中的微调
3. **质量监控**：实时监控数据质量
4. **记录校准参数**：保存校准结果用于分析

这个详细的原理说明为开发校准程序提供了理论基础。接下来我们将基于这些原理实现具体的校准程序。
