#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据管理模块
处理实验数据的记录、保存和分析
"""

import os
import json
import csv
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any

class DataManager:
    """实验数据管理类"""

    def __init__(self, participant_id: str, base_data_dir: str = "data"):
        """
        初始化数据管理器

        Args:
            participant_id: 被试ID
            base_data_dir: 基础数据目录
        """
        # 创建基础data目录
        os.makedirs(base_data_dir, exist_ok=True)

        # 生成数据子目录名：日期+时间+ID
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        dir_name = f"{timestamp}_{participant_id}"
        self.data_dir = os.path.join(base_data_dir, dir_name)
        os.makedirs(self.data_dir, exist_ok=True)

        self.participant_id = participant_id
        self.timestamp = timestamp
        
        # 数据文件路径
        self.summary_file = os.path.join(self.data_dir, "experiment_summary.json")
        self.csv_file = os.path.join(self.data_dir, "experiment_data.csv")
        self.log_file = os.path.join(self.data_dir, "experiment_log.txt")
        self.edf_file = os.path.join(self.data_dir, f"{participant_id}.edf")
        
        # 初始化日志
        self._init_log()
    
    def _init_log(self):
        """初始化日志文件"""
        with open(self.log_file, 'w', encoding='utf-8') as f:
            f.write(f"实验日志 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 50 + "\n\n")
    
    def log_event(self, event: str, details: str = ""):
        """
        记录事件到日志
        
        Args:
            event: 事件名称
            details: 事件详情
        """
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        log_entry = f"[{timestamp}] {event}"
        if details:
            log_entry += f" - {details}"
        log_entry += "\n"
        
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception as e:
            print(f"写入日志失败: {e}")
    
    def save_trial_data(self, trial_data: Dict) -> bool:
        """
        保存单个试次数据
        
        Args:
            trial_data: 试次数据字典
            
        Returns:
            保存是否成功
        """
        try:
            # 保存JSON格式
            trial_file = os.path.join(self.data_dir, f"trial_{trial_data['trial_num']}.json")
            with open(trial_file, 'w', encoding='utf-8') as f:
                json.dump(trial_data, f, ensure_ascii=False, indent=2)
            
            # 记录日志
            self.log_event("TRIAL_SAVED", f"Trial {trial_data['trial_num']} saved to {trial_file}")
            
            return True
            
        except Exception as e:
            self.log_event("ERROR", f"Failed to save trial {trial_data.get('trial_num', 'unknown')}: {e}")
            return False
    
    def save_experiment_summary(self, experiment_data: Dict) -> bool:
        """
        保存实验总结数据
        
        Args:
            experiment_data: 实验数据字典
            
        Returns:
            保存是否成功
        """
        try:
            # 保存JSON格式
            with open(self.summary_file, 'w', encoding='utf-8') as f:
                json.dump(experiment_data, f, ensure_ascii=False, indent=2)
            
            # 保存CSV格式（便于分析）
            self._save_csv_data(experiment_data)
            
            self.log_event("EXPERIMENT_SAVED", f"Experiment summary saved")
            
            return True
            
        except Exception as e:
            self.log_event("ERROR", f"Failed to save experiment summary: {e}")
            return False
    
    def _save_csv_data(self, experiment_data: Dict):
        """将数据保存为CSV格式"""
        try:
            trials = experiment_data.get('trials', [])
            if not trials:
                return
            
            # 准备CSV数据
            csv_data = []
            for trial in trials:
                row = {
                    'participant_id': experiment_data.get('participant_id', ''),
                    'trial_num': trial.get('trial_num', ''),
                    'question_id': trial.get('question_id', ''),
                    'question': trial.get('question', ''),
                    'answer': trial.get('answer', ''),
                    'participant_response': trial.get('participant_response', ''),
                    'curiosity_rating': trial.get('curiosity_rating', ''),
                    'pleasure_rating': trial.get('pleasure_rating', ''),
                    'surprise_rating': trial.get('surprise_rating', ''),
                    'start_time': trial.get('start_time', ''),
                    'end_time': trial.get('end_time', ''),
                }
                
                # 添加时间数据
                timing_data = trial.get('timing_data', {})
                for key, value in timing_data.items():
                    row[f'timing_{key}'] = value
                
                csv_data.append(row)
            
            # 写入CSV文件
            if csv_data:
                df = pd.DataFrame(csv_data)
                df.to_csv(self.csv_file, index=False, encoding='utf-8-sig')
                self.log_event("CSV_SAVED", f"Data saved to {self.csv_file}")
                
        except Exception as e:
            self.log_event("ERROR", f"Failed to save CSV data: {e}")
    
    def load_experiment_data(self) -> Optional[Dict]:
        """
        加载实验数据
        
        Returns:
            实验数据字典，如果加载失败则返回None
        """
        try:
            if os.path.exists(self.summary_file):
                with open(self.summary_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self.log_event("DATA_LOADED", f"Experiment data loaded from {self.summary_file}")
                return data
            else:
                self.log_event("WARNING", f"Summary file not found: {self.summary_file}")
                return None
                
        except Exception as e:
            self.log_event("ERROR", f"Failed to load experiment data: {e}")
            return None
    
    def get_data_summary(self) -> Dict:
        """
        获取数据摘要统计
        
        Returns:
            数据摘要字典
        """
        summary = {
            'data_dir': self.data_dir,
            'files_created': [],
            'total_trials': 0,
            'completion_rate': 0.0,
            'average_ratings': {}
        }
        
        try:
            # 检查文件
            for filename in os.listdir(self.data_dir):
                if filename.endswith('.json') or filename.endswith('.csv') or filename.endswith('.txt'):
                    summary['files_created'].append(filename)
            
            # 加载实验数据进行统计
            experiment_data = self.load_experiment_data()
            if experiment_data:
                trials = experiment_data.get('trials', [])
                summary['total_trials'] = len(trials)
                
                # 计算完成率
                completed_trials = sum(1 for trial in trials if trial.get('end_time'))
                if trials:
                    summary['completion_rate'] = completed_trials / len(trials) * 100
                
                # 计算平均评分
                curiosity_ratings = [trial.get('curiosity_rating') for trial in trials 
                                   if trial.get('curiosity_rating') is not None]
                pleasure_ratings = [trial.get('pleasure_rating') for trial in trials 
                                  if trial.get('pleasure_rating') is not None]
                surprise_ratings = [trial.get('surprise_rating') for trial in trials 
                                  if trial.get('surprise_rating') is not None]
                
                if curiosity_ratings:
                    summary['average_ratings']['curiosity'] = sum(curiosity_ratings) / len(curiosity_ratings)
                if pleasure_ratings:
                    summary['average_ratings']['pleasure'] = sum(pleasure_ratings) / len(pleasure_ratings)
                if surprise_ratings:
                    summary['average_ratings']['surprise'] = sum(surprise_ratings) / len(surprise_ratings)
            
        except Exception as e:
            self.log_event("ERROR", f"Failed to generate data summary: {e}")
        
        return summary
    
    def backup_data(self, backup_dir: str = None) -> bool:
        """
        备份实验数据
        
        Args:
            backup_dir: 备份目录，如果为None则使用默认备份目录
            
        Returns:
            备份是否成功
        """
        if backup_dir is None:
            backup_dir = f"{self.data_dir}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            import shutil
            shutil.copytree(self.data_dir, backup_dir)
            self.log_event("BACKUP_CREATED", f"Data backed up to {backup_dir}")
            return True
            
        except Exception as e:
            self.log_event("ERROR", f"Failed to backup data: {e}")
            return False
    
    def validate_data(self) -> Dict:
        """
        验证数据完整性
        
        Returns:
            验证结果字典
        """
        validation_result = {
            'is_valid': True,
            'issues': [],
            'warnings': []
        }
        
        try:
            # 检查必要文件是否存在
            if not os.path.exists(self.summary_file):
                validation_result['issues'].append("实验总结文件缺失")
                validation_result['is_valid'] = False
            
            # 加载并检查数据
            experiment_data = self.load_experiment_data()
            if experiment_data:
                trials = experiment_data.get('trials', [])
                
                # 检查试次数据完整性
                for trial in trials:
                    trial_num = trial.get('trial_num')
                    
                    # 检查必要字段
                    required_fields = ['question', 'answer', 'start_time', 'end_time']
                    for field in required_fields:
                        if not trial.get(field):
                            validation_result['issues'].append(f"试次 {trial_num} 缺少字段: {field}")
                            validation_result['is_valid'] = False
                    
                    # 检查评分数据
                    if trial.get('curiosity_rating') is None:
                        validation_result['warnings'].append(f"试次 {trial_num} 缺少好奇心评分")
                    if trial.get('pleasure_rating') is None:
                        validation_result['warnings'].append(f"试次 {trial_num} 缺少愉悦度评分")
                    if trial.get('surprise_rating') is None:
                        validation_result['warnings'].append(f"试次 {trial_num} 缺少意外程度评分")
            
        except Exception as e:
            validation_result['issues'].append(f"数据验证过程出错: {e}")
            validation_result['is_valid'] = False
        
        return validation_result

def test_data_manager():
    """测试数据管理功能"""
    print("测试数据管理功能...")

    # 创建测试数据管理器
    data_manager = DataManager("test_001")
    
    # 测试日志记录
    data_manager.log_event("TEST_START", "开始测试数据管理功能")
    
    # 创建测试数据
    test_trial = {
        'trial_num': 1,
        'question_id': 1,
        'question': '测试问题',
        'answer': '测试答案',
        'participant_response': '被试答案',
        'curiosity_rating': 4,
        'pleasure_rating': 3,
        'surprise_rating': 2,
        'start_time': datetime.now().isoformat(),
        'end_time': datetime.now().isoformat(),
        'timing_data': {
            'question_duration': 8.0,
            'input_duration': 10.0
        }
    }
    
    test_experiment = {
        'participant_id': 'test_001',
        'total_trials': 1,
        'trials': [test_trial]
    }
    
    # 测试保存功能
    print("测试保存功能...")
    data_manager.save_trial_data(test_trial)
    data_manager.save_experiment_summary(test_experiment)
    
    # 测试加载功能
    print("测试加载功能...")
    loaded_data = data_manager.load_experiment_data()
    print(f"加载的数据: {loaded_data is not None}")
    
    # 测试数据摘要
    print("测试数据摘要...")
    summary = data_manager.get_data_summary()
    print(f"数据摘要: {summary}")
    
    # 测试数据验证
    print("测试数据验证...")
    validation = data_manager.validate_data()
    print(f"验证结果: {validation}")
    
    data_manager.log_event("TEST_END", "数据管理功能测试完成")
    print(f"测试完成，数据保存在: {data_manager.data_dir}")

if __name__ == "__main__":
    test_data_manager()
