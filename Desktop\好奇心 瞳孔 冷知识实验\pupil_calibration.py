#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
EyeLink瞳孔校准程序
独立的校准工具，包括校准、验证和实时眼动显示功能
"""

import os
import sys
import time
import math
from datetime import datetime
from typing import Tuple, Optional, List

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import pylink
    PYLINK_AVAILABLE = True
except ImportError:
    PYLINK_AVAILABLE = False
    print("警告：PyLink未安装，将使用模拟模式")

try:
    from psychopy import visual, core, event
    PSYCHOPY_AVAILABLE = True
except ImportError:
    PSYCHOPY_AVAILABLE = False
    print("警告：PsychoPy未安装，将使用模拟模式")

class PupilCalibrationSystem:
    """瞳孔校准系统主类"""
    
    def __init__(self, participant_id: str = "calibration_test", 
                 screen_size: Tuple[int, int] = (1920, 1080),
                 fullscreen: bool = True,
                 dummy_mode: bool = False):
        """
        初始化校准系统
        
        Args:
            participant_id: 被试ID
            screen_size: 屏幕尺寸
            fullscreen: 是否全屏显示
            dummy_mode: 是否使用虚拟模式
        """
        self.participant_id = participant_id
        self.screen_size = screen_size
        self.fullscreen = fullscreen
        self.dummy_mode = dummy_mode or not (PYLINK_AVAILABLE and PSYCHOPY_AVAILABLE)
        
        # EyeLink相关
        self.tracker = None
        self.is_connected = False
        self.is_recording = False
        
        # PsychoPy相关
        self.win = None
        self.display_ready = False
        
        # 校准参数
        self.calibration_points = []
        self.validation_points = []
        self.corner_points = []
        
        # 实时显示参数
        self.gaze_cursor = None
        self.current_gaze = (0, 0)
        self.gaze_history = []
        
        print(f"校准系统初始化 - 被试ID: {participant_id}")
        print(f"模式: {'虚拟模式' if self.dummy_mode else '真实模式'}")
    
    def initialize_system(self) -> bool:
        """
        初始化整个系统
        
        Returns:
            初始化是否成功
        """
        print("正在初始化校准系统...")
        
        # 初始化显示系统
        if not self._initialize_display():
            print("显示系统初始化失败")
            return False
        
        # 初始化EyeLink
        if not self._initialize_eyelink():
            print("EyeLink初始化失败")
            return False
        
        # 设置校准点
        self._setup_calibration_points()
        
        print("校准系统初始化完成")
        return True
    
    def _initialize_display(self) -> bool:
        """初始化PsychoPy显示"""
        if self.dummy_mode:
            print("虚拟模式：跳过显示初始化")
            self.display_ready = True
            return True
        
        try:
            self.win = visual.Window(
                size=self.screen_size,
                fullscr=self.fullscreen,
                screen=0,
                allowGUI=True,
                monitor='testMonitor',
                color=[0, 0, 0],  # 黑色背景
                colorSpace='rgb',
                units='pix'
            )
            
            # 创建注视点光标
            self.gaze_cursor = visual.Circle(
                self.win,
                radius=10,
                fillColor='red',
                lineColor='white',
                lineWidth=2
            )
            
            self.display_ready = True
            print("✓ PsychoPy显示系统初始化成功")
            return True
            
        except Exception as e:
            print(f"✗ PsychoPy显示初始化失败: {e}")
            self.dummy_mode = True
            self.display_ready = True
            return True
    
    def _initialize_eyelink(self) -> bool:
        """初始化EyeLink连接"""
        if self.dummy_mode:
            print("虚拟模式：跳过EyeLink初始化")
            self.is_connected = True
            return True
        
        try:
            # 连接EyeLink
            self.tracker = pylink.EyeLink("100.1.1.1")
            
            # 设置屏幕分辨率
            self.tracker.sendCommand(f"screen_pixel_coords = 0 0 {self.screen_size[0]-1} {self.screen_size[1]-1}")
            
            # 设置采样率
            self.tracker.sendCommand("sample_rate 1000")
            
            # 设置数据记录类型
            self.tracker.sendCommand("file_event_filter = LEFT,RIGHT,FIXATION,SACCADE,BLINK,MESSAGE")
            self.tracker.sendCommand("file_sample_data = LEFT,RIGHT,GAZE,AREA,GAZERES,STATUS")
            self.tracker.sendCommand("link_sample_data = LEFT,RIGHT,GAZE,AREA,GAZERES,STATUS")
            
            # 打开数据文件
            edf_filename = f"{self.participant_id[:8]}.EDF"
            self.tracker.openDataFile(edf_filename)
            
            self.is_connected = True
            print("✓ EyeLink连接成功")
            return True
            
        except Exception as e:
            print(f"✗ EyeLink连接失败: {e}")
            print("切换到虚拟模式")
            self.dummy_mode = True
            self.is_connected = True
            return True
    
    def _setup_calibration_points(self):
        """设置校准点和验证点位置"""
        w, h = self.screen_size
        
        # 9点校准 (标准EyeLink校准)
        self.calibration_points = [
            (w*0.1, h*0.1),   # 左上
            (w*0.5, h*0.1),   # 中上
            (w*0.9, h*0.1),   # 右上
            (w*0.1, h*0.5),   # 左中
            (w*0.5, h*0.5),   # 中心
            (w*0.9, h*0.5),   # 右中
            (w*0.1, h*0.9),   # 左下
            (w*0.5, h*0.9),   # 中下
            (w*0.9, h*0.9),   # 右下
        ]
        
        # 5点验证
        self.validation_points = [
            (w*0.2, h*0.2),   # 左上
            (w*0.8, h*0.2),   # 右上
            (w*0.5, h*0.5),   # 中心
            (w*0.2, h*0.8),   # 左下
            (w*0.8, h*0.8),   # 右下
        ]
        
        # 四角验证点
        self.corner_points = [
            (w*0.05, h*0.05),  # 左上角
            (w*0.95, h*0.05),  # 右上角
            (w*0.05, h*0.95),  # 左下角
            (w*0.95, h*0.95),  # 右下角
        ]
    
    def run_calibration(self) -> bool:
        """
        运行EyeLink校准
        
        Returns:
            校准是否成功
        """
        print("\n开始EyeLink校准...")
        
        if self.dummy_mode:
            print("虚拟模式：模拟校准过程")
            self._simulate_calibration()
            return True
        
        try:
            # 使用EyeLink内置校准
            self.tracker.doTrackerSetup()
            print("✓ EyeLink校准完成")
            return True
            
        except Exception as e:
            print(f"✗ EyeLink校准失败: {e}")
            return False
    
    def _simulate_calibration(self):
        """模拟校准过程（虚拟模式）"""
        if not self.display_ready:
            return
        
        print("模拟9点校准过程...")
        
        for i, (x, y) in enumerate(self.calibration_points, 1):
            print(f"校准点 {i}/9: ({x:.0f}, {y:.0f})")
            
            if not self.dummy_mode:
                # 显示校准点
                calibration_dot = visual.Circle(
                    self.win,
                    radius=15,
                    fillColor='white',
                    pos=(x - self.screen_size[0]/2, self.screen_size[1]/2 - y)
                )
                
                # 显示校准点2秒
                for _ in range(60):  # 约2秒 (假设60fps)
                    calibration_dot.draw()
                    self.win.flip()
                    
                    # 检查退出
                    keys = event.getKeys()
                    if 'escape' in keys:
                        return
            else:
                time.sleep(0.5)  # 虚拟模式快速模拟
        
        print("模拟校准完成")
    
    def start_real_time_display(self):
        """
        开始实时眼动显示
        """
        print("\n开始实时眼动显示...")
        print("按ESC键退出，按SPACE键重新校准")
        
        if self.dummy_mode:
            self._simulate_real_time_display()
            return
        
        try:
            # 开始记录
            self.tracker.startRecording(1, 1, 1, 1)
            self.is_recording = True
            
            # 实时显示循环
            self._real_time_display_loop()
            
        except Exception as e:
            print(f"实时显示出错: {e}")
        finally:
            if self.is_recording:
                self.tracker.stopRecording()
                self.is_recording = False
    
    def _real_time_display_loop(self):
        """实时显示主循环"""
        clock = core.Clock() if not self.dummy_mode else None
        
        while True:
            # 获取当前眼动数据
            gaze_pos = self._get_current_gaze()
            
            if gaze_pos:
                self.current_gaze = gaze_pos
                self.gaze_history.append(gaze_pos)
                
                # 限制历史记录长度
                if len(self.gaze_history) > 100:
                    self.gaze_history.pop(0)
            
            # 更新显示
            self._update_display()
            
            # 处理按键
            keys = event.getKeys() if not self.dummy_mode else []
            if 'escape' in keys:
                break
            elif 'space' in keys:
                print("重新校准...")
                self.run_calibration()
            elif 'c' in keys:
                print("显示四角验证点...")
                self._show_corner_validation()
            
            # 控制帧率
            if clock:
                clock.tick(60)
            else:
                time.sleep(1/60)
    
    def _get_current_gaze(self) -> Optional[Tuple[float, float]]:
        """获取当前注视点位置"""
        if self.dummy_mode:
            # 模拟眼动数据（鼠标位置）
            if PSYCHOPY_AVAILABLE and self.win:
                mouse_pos = event.Mouse(win=self.win).getPos()
                # 转换坐标系
                x = mouse_pos[0] + self.screen_size[0]/2
                y = self.screen_size[1]/2 - mouse_pos[1]
                return (x, y)
            else:
                # 随机模拟
                import random
                x = random.uniform(0, self.screen_size[0])
                y = random.uniform(0, self.screen_size[1])
                return (x, y)
        
        try:
            # 获取最新样本
            sample = self.tracker.getNewestSample()
            if sample is not None:
                if sample.isRightSample():
                    gaze = sample.getRightEye().getGaze()
                elif sample.isLeftSample():
                    gaze = sample.getLeftEye().getGaze()
                else:
                    return None
                
                # 检查数据有效性
                if gaze[0] != pylink.MISSING_DATA and gaze[1] != pylink.MISSING_DATA:
                    return gaze
            
        except Exception as e:
            print(f"获取眼动数据失败: {e}")
        
        return None
    
    def _update_display(self):
        """更新显示内容"""
        if self.dummy_mode and not PSYCHOPY_AVAILABLE:
            return
        
        # 清除屏幕
        self.win.clearBuffer()
        
        # 绘制四角标定点
        self._draw_corner_points()
        
        # 绘制当前注视点
        if self.current_gaze:
            x, y = self.current_gaze
            # 转换坐标系（PsychoPy使用中心为原点）
            psychopy_x = x - self.screen_size[0]/2
            psychopy_y = self.screen_size[1]/2 - y
            
            self.gaze_cursor.pos = (psychopy_x, psychopy_y)
            self.gaze_cursor.draw()
        
        # 绘制注视轨迹
        self._draw_gaze_trail()
        
        # 绘制信息文本
        self._draw_info_text()
        
        # 更新屏幕
        self.win.flip()
    
    def _draw_corner_points(self):
        """绘制四角标定点"""
        for x, y in self.corner_points:
            # 转换坐标系
            psychopy_x = x - self.screen_size[0]/2
            psychopy_y = self.screen_size[1]/2 - y
            
            corner_dot = visual.Circle(
                self.win,
                radius=8,
                fillColor='green',
                lineColor='white',
                pos=(psychopy_x, psychopy_y)
            )
            corner_dot.draw()
    
    def _draw_gaze_trail(self):
        """绘制注视轨迹"""
        if len(self.gaze_history) < 2:
            return
        
        # 绘制最近的轨迹点
        for i, (x, y) in enumerate(self.gaze_history[-20:]):  # 最近20个点
            alpha = (i + 1) / 20  # 透明度渐变
            psychopy_x = x - self.screen_size[0]/2
            psychopy_y = self.screen_size[1]/2 - y
            
            trail_dot = visual.Circle(
                self.win,
                radius=3,
                fillColor='yellow',
                opacity=alpha * 0.5,
                pos=(psychopy_x, psychopy_y)
            )
            trail_dot.draw()
    
    def _draw_info_text(self):
        """绘制信息文本"""
        info_lines = [
            f"被试ID: {self.participant_id}",
            f"当前注视点: {self.current_gaze[0]:.1f}, {self.current_gaze[1]:.1f}" if self.current_gaze else "当前注视点: 无数据",
            f"模式: {'虚拟模式' if self.dummy_mode else '真实模式'}",
            "",
            "操作说明:",
            "ESC - 退出程序",
            "SPACE - 重新校准", 
            "C - 显示四角验证"
        ]
        
        for i, line in enumerate(info_lines):
            info_text = visual.TextStim(
                self.win,
                text=line,
                font='SimHei',
                pos=(-self.screen_size[0]/2 + 20, self.screen_size[1]/2 - 30 - i*25),
                height=20,
                color='white',
                anchorHoriz='left',
                anchorVert='top'
            )
            info_text.draw()
    
    def _show_corner_validation(self):
        """显示四角验证点"""
        print("显示四角验证点，请依次注视各个角落的绿色圆点")
        
        for i, (x, y) in enumerate(self.corner_points):
            print(f"请注视第 {i+1} 个角落点")
            
            # 高亮当前验证点
            for _ in range(180):  # 3秒
                self.win.clearBuffer()
                
                # 绘制所有角落点
                self._draw_corner_points()
                
                # 高亮当前点
                psychopy_x = x - self.screen_size[0]/2
                psychopy_y = self.screen_size[1]/2 - y
                
                highlight_dot = visual.Circle(
                    self.win,
                    radius=15,
                    fillColor='red',
                    lineColor='yellow',
                    lineWidth=3,
                    pos=(psychopy_x, psychopy_y)
                )
                highlight_dot.draw()
                
                # 绘制当前注视点
                gaze_pos = self._get_current_gaze()
                if gaze_pos:
                    gx, gy = gaze_pos
                    gaze_x = gx - self.screen_size[0]/2
                    gaze_y = self.screen_size[1]/2 - gy
                    self.gaze_cursor.pos = (gaze_x, gaze_y)
                    self.gaze_cursor.draw()
                
                self.win.flip()
                
                # 检查退出
                keys = event.getKeys()
                if 'escape' in keys:
                    return
    
    def _simulate_real_time_display(self):
        """模拟实时显示（虚拟模式）"""
        print("虚拟模式：模拟实时眼动显示")
        print("模拟数据将显示在控制台...")
        
        import random
        
        for i in range(100):  # 模拟100个数据点
            x = random.uniform(0, self.screen_size[0])
            y = random.uniform(0, self.screen_size[1])
            print(f"模拟注视点 {i+1}: ({x:.1f}, {y:.1f})")
            time.sleep(0.1)
            
            if i % 20 == 0:
                print("按任意键继续...")
                input()
    
    def cleanup(self):
        """清理资源"""
        print("正在清理资源...")
        
        if self.is_recording and self.tracker:
            self.tracker.stopRecording()
        
        if self.tracker and not self.dummy_mode:
            try:
                self.tracker.closeDataFile()
                # 接收EDF文件
                edf_local = f"{self.participant_id}_calibration.edf"
                self.tracker.receiveDataFile(f"{self.participant_id[:8]}.EDF", edf_local)
                self.tracker.close()
                print(f"✓ EDF文件已保存: {edf_local}")
            except Exception as e:
                print(f"清理EyeLink时出错: {e}")
        
        if self.win:
            self.win.close()
        
        print("资源清理完成")

def main():
    """主函数"""
    print("EyeLink瞳孔校准程序")
    print("=" * 50)
    
    # 获取被试信息
    participant_id = input("请输入被试ID (默认: calibration_test): ").strip()
    if not participant_id:
        participant_id = "calibration_test"
    
    # 创建校准系统
    calibration_system = PupilCalibrationSystem(
        participant_id=participant_id,
        fullscreen=True,
        dummy_mode=False
    )
    
    try:
        # 初始化系统
        if not calibration_system.initialize_system():
            print("系统初始化失败")
            return
        
        # 运行校准
        if not calibration_system.run_calibration():
            print("校准失败")
            return
        
        # 开始实时显示
        calibration_system.start_real_time_display()
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n程序出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        calibration_system.cleanup()

if __name__ == "__main__":
    main()
