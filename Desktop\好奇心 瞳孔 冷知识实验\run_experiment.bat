@echo off
echo ========================================
echo 好奇心瞳孔冷知识实验启动器
echo ========================================
echo.

REM 激活conda环境
echo 激活eyetracking环境...
call conda activate eyetracking

REM 检查是否成功激活
if errorlevel 1 (
    echo 错误：无法激活eyetracking环境
    echo 请确保已安装conda并创建了eyetracking环境
    pause
    exit /b 1
)

echo 环境激活成功！
echo.

REM 运行实验程序
echo 启动实验程序...
python main_experiment.py

REM 保持窗口打开
echo.
echo 实验程序已结束
pause
