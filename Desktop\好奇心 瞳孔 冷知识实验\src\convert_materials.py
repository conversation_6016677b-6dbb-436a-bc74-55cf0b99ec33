#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验材料转换脚本
将实验材料库.txt转换为JSON格式
"""

import json
import re
import os
from typing import List, Dict, Any


class MaterialConverter:
    """实验材料转换器"""
    
    def __init__(self, input_file: str, output_file: str):
        self.input_file = input_file
        self.output_file = output_file
        self.questions = []
        self.progressive_sequences = []
        
    def read_materials(self) -> str:
        """读取原始材料文件"""
        with open(self.input_file, 'r', encoding='utf-8') as f:
            return f.read()
    
    def parse_general_questions(self, content: str) -> List[Dict]:
        """解析一般冷知识题目（第31-164行）"""
        questions = []
        lines = content.split('\n')
        
        # 提取题目部分（第31-164行）
        question_lines = lines[32:164]  # 0-based索引
        
        for i, line in enumerate(question_lines, 1):
            line = line.strip()
            if line and not line.startswith('*'):
                # 移除行首的数字编号
                question_text = re.sub(r'^\d+\.\s*', '', line)
                if question_text:
                    questions.append({
                        'id': f'general_{i:03d}',
                        'question': question_text,
                        'answer': '',  # 稍后填充
                        'category': 'general',
                        'difficulty': 3,  # 默认中等难度
                        'subject': 'general',
                        'question_type': 'fill_blank',
                        'expected_curiosity': 3,
                        'answer_length': 'medium',
                        'tags': ['冷知识'],
                        'source': '实验材料库',
                        'notes': ''
                    })
        
        return questions
    
    def parse_simple_questions(self, content: str) -> List[Dict]:
        """解析简单题目（第165-205行）"""
        questions = []
        lines = content.split('\n')
        
        # 提取简单题目部分
        question_lines = lines[164:205]
        
        for i, line in enumerate(question_lines, 1):
            line = line.strip()
            if line and not line.startswith('*'):
                question_text = re.sub(r'^\d+\.\s*', '', line)
                if question_text:
                    questions.append({
                        'id': f'simple_{i:03d}',
                        'question': question_text,
                        'answer': '',
                        'category': 'simple',
                        'difficulty': 1,
                        'subject': 'general',
                        'question_type': 'fill_blank',
                        'expected_curiosity': 2,
                        'answer_length': 'short',
                        'tags': ['基础知识'],
                        'source': '实验材料库',
                        'notes': ''
                    })
        
        return questions
    
    def parse_difficult_questions(self, content: str) -> List[Dict]:
        """解析高难度题目（第206-244行）"""
        questions = []
        lines = content.split('\n')
        
        # 提取高难度题目部分
        question_lines = lines[205:244]
        
        for i, line in enumerate(question_lines, 1):
            line = line.strip()
            if line and not line.startswith('*'):
                question_text = re.sub(r'^\d+\.\s*', '', line)
                if question_text:
                    questions.append({
                        'id': f'difficult_{i:03d}',
                        'question': question_text,
                        'answer': '',
                        'category': 'difficult',
                        'difficulty': 5,
                        'subject': 'specialized',
                        'question_type': 'fill_blank',
                        'expected_curiosity': 4,
                        'answer_length': 'long',
                        'tags': ['专业知识', '高难度'],
                        'source': '实验材料库',
                        'notes': ''
                    })
        
        return questions
    
    def parse_progressive_sequences(self, content: str) -> List[Dict]:
        """解析递进式题目序列"""
        sequences = []
        lines = content.split('\n')
        
        # 学科映射
        subjects = {
            '物理学': 'physics',
            '化学': 'chemistry', 
            '生物学': 'biology',
            '数学': 'mathematics',
            '计算机科学': 'computer_science',
            '心理学': 'psychology',
            '经济学': 'economics',
            '历史学': 'history',
            '哲学': 'philosophy',
            '社会学': 'sociology'
        }
        
        current_subject = None
        current_questions = []
        question_counter = 0
        
        # 从第530行开始解析递进题目
        for line_num, line in enumerate(lines[529:], 530):
            line = line.strip()
            
            # 检测学科标题
            for chinese_name, english_name in subjects.items():
                if chinese_name in line and '(' in line:
                    if current_subject and current_questions:
                        # 保存前一个学科的题目
                        sequences.append({
                            'sequence_id': current_subject,
                            'subject': current_subject,
                            'description': f'{current_subject}递进式学习序列',
                            'questions': current_questions
                        })
                    
                    current_subject = english_name
                    current_questions = []
                    question_counter = 0
                    break
            
            # 解析题目
            if line and re.match(r'^\d+\.\s*', line) and current_subject:
                question_counter += 1
                question_text = re.sub(r'^\d+\.\s*', '', line)
                current_questions.append({
                    'step': question_counter,
                    'question': question_text,
                    'answer': '',
                    'builds_on': f'step_{question_counter-1}' if question_counter > 1 else 'foundation'
                })
        
        # 保存最后一个学科
        if current_subject and current_questions:
            sequences.append({
                'sequence_id': current_subject,
                'subject': current_subject,
                'description': f'{current_subject}递进式学习序列',
                'questions': current_questions
            })
        
        return sequences
    
    def parse_answers(self, content: str) -> Dict[str, str]:
        """解析答案部分"""
        answers = {}
        lines = content.split('\n')
        
        # 从第279行开始解析答案
        answer_lines = lines[278:523]
        
        for line in answer_lines:
            line = line.strip()
            if line and re.match(r'^\d+\.\s*', line):
                # 提取答案编号和内容
                match = re.match(r'^(\d+)\.\s*(.+)', line)
                if match:
                    answer_num = int(match.group(1))
                    answer_text = match.group(2)
                    answers[answer_num] = answer_text
        
        return answers
    
    def convert(self):
        """执行转换"""
        print("开始转换实验材料...")
        
        # 读取原始文件
        content = self.read_materials()
        
        # 解析各部分
        general_questions = self.parse_general_questions(content)
        simple_questions = self.parse_simple_questions(content)
        difficult_questions = self.parse_difficult_questions(content)
        progressive_sequences = self.parse_progressive_sequences(content)
        answers = self.parse_answers(content)
        
        # 填充答案
        all_questions = general_questions + simple_questions + difficult_questions
        for i, question in enumerate(all_questions):
            if i + 1 in answers:
                question['answer'] = answers[i + 1]
        
        # 构建最终数据结构
        data = {
            'schema_version': '1.0',
            'created_date': '2025-07-05',
            'description': '好奇心瞳孔冷知识实验题目数据库',
            'total_questions': len(all_questions),
            'questions': all_questions,
            'progressive_sequences': progressive_sequences,
            'statistics': {
                'general_questions': len(general_questions),
                'simple_questions': len(simple_questions),
                'difficult_questions': len(difficult_questions),
                'progressive_sequences': len(progressive_sequences)
            }
        }
        
        # 保存JSON文件
        os.makedirs(os.path.dirname(self.output_file), exist_ok=True)
        with open(self.output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"转换完成！共处理 {len(all_questions)} 道题目")
        print(f"输出文件：{self.output_file}")


if __name__ == '__main__':
    # 设置文件路径
    input_file = '../实验材料库.txt'
    output_file = '../stimuli/questions.json'
    
    # 执行转换
    converter = MaterialConverter(input_file, output_file)
    converter.convert()
