#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试改进后的实验程序
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_data_manager():
    """测试新的数据管理器"""
    print("=" * 50)
    print("测试数据管理器")
    print("=" * 50)
    
    try:
        from data_manager import DataManager
        
        # 创建数据管理器
        dm = DataManager("test001")
        print(f"✓ 数据目录创建成功: {dm.data_dir}")
        
        # 测试日志记录
        dm.log_event("TEST_START", "开始测试数据管理器")
        print("✓ 日志记录成功")
        
        # 测试数据保存
        test_trial = {
            'trial_num': 1,
            'question': '测试问题',
            'answer': '测试答案',
            'participant_response': '被试答案',
            'curiosity_rating': 4
        }
        
        dm.save_trial_data(test_trial)
        print("✓ 试次数据保存成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据管理器测试失败: {e}")
        return False

def test_eyelink_manager():
    """测试新的EyeLink管理器"""
    print("=" * 50)
    print("测试EyeLink管理器")
    print("=" * 50)
    
    try:
        from eyelink_manager import EyeLinkManager
        
        # 创建EyeLink管理器（虚拟模式）
        el = EyeLinkManager("test001", "test_data", dummy_mode=True)
        print("✓ EyeLink管理器创建成功")
        
        # 测试连接
        if el.connect():
            print("✓ 连接成功")
            
            # 测试配置
            if el.setup_tracker():
                print("✓ 配置成功")
                
                # 测试事件记录
                el.log_experiment_start()
                el.log_trial_start(1, "测试题目")
                el.log_baseline_start("FIXATION")
                el.log_baseline_end("FIXATION")
                el.log_question_display("1+1=?")
                el.log_question_end()
                el.log_input_start()
                el.log_input_end("2")
                el.log_rating_start("CURIOSITY")
                el.log_rating_end("CURIOSITY", 3)
                el.log_answer_display("答案是2")
                el.log_answer_end()
                el.log_trial_end(1)
                el.log_experiment_end()
                print("✓ 事件记录成功")
                
                # 关闭连接
                el.close()
                print("✓ 关闭连接成功")
                
                return True
        
        return False
        
    except Exception as e:
        print(f"✗ EyeLink管理器测试失败: {e}")
        return False

def test_experiment_materials():
    """测试实验材料管理器"""
    print("=" * 50)
    print("测试实验材料管理器")
    print("=" * 50)
    
    try:
        from experiment_materials import ExperimentMaterials
        
        # 创建材料管理器
        materials = ExperimentMaterials()
        print(f"✓ 材料加载成功，共 {len(materials.question_answer_pairs)} 道题目")
        
        # 测试随机选择
        selected = materials.get_random_questions(3)
        print(f"✓ 随机选择成功，选中 {len(selected)} 道题目")
        
        # 显示选中的题目
        for i, qa in enumerate(selected, 1):
            print(f"  {i}. {qa['question'][:30]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ 实验材料测试失败: {e}")
        return False

def test_experiment_display():
    """测试实验显示管理器"""
    print("=" * 50)
    print("测试实验显示管理器")
    print("=" * 50)
    
    try:
        from experiment_display import ExperimentDisplay
        
        # 创建显示管理器
        display = ExperimentDisplay(fullscreen=False)
        print("✓ 显示管理器创建成功")
        
        # 关闭显示
        display.close()
        print("✓ 显示管理器关闭成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 显示管理器测试失败: {e}")
        return False

def test_experiment_flow():
    """测试实验流程控制器"""
    print("=" * 50)
    print("测试实验流程控制器")
    print("=" * 50)
    
    try:
        from experiment_flow import CuriosityExperiment
        
        # 创建实验对象
        experiment = CuriosityExperiment("test001", use_eyelink=False, fullscreen=False)
        print("✓ 实验对象创建成功")
        print(f"✓ 数据目录: {experiment.data_dir}")
        
        # 清理资源
        experiment.cleanup()
        print("✓ 资源清理成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 实验流程测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("测试改进后的实验程序")
    print("=" * 60)
    
    tests = [
        ("数据管理器", test_data_manager),
        ("EyeLink管理器", test_eyelink_manager),
        ("实验材料管理器", test_experiment_materials),
        ("实验显示管理器", test_experiment_display),
        ("实验流程控制器", test_experiment_flow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"✗ {test_name} 测试出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！程序改进成功！")
    else:
        print("⚠ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
