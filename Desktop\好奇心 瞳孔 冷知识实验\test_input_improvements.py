#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试输入功能改进
专门测试中文输入和回车确认功能
"""

import os
import sys
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_text_input():
    """测试文本输入功能"""
    print("=" * 60)
    print("测试文本输入功能改进")
    print("=" * 60)
    
    try:
        from experiment_display import ExperimentDisplay
        
        print("创建显示管理器...")
        display = ExperimentDisplay(fullscreen=False)
        
        if display.dummy_mode:
            print("⚠ 当前为模拟模式，无法测试真实的输入功能")
            print("✓ 模拟模式测试通过")
            return True
        
        print("✓ 显示管理器创建成功")
        print("\n开始输入测试...")
        print("请在弹出的窗口中进行以下测试：")
        print("1. 输入英文字符")
        print("2. 输入中文字符")
        print("3. 测试退格删除")
        print("4. 按回车确认")
        print("5. 按ESC取消")
        
        # 测试1：基本输入
        print("\n测试1：基本文本输入")
        result1 = display.get_text_input("请输入一些文字（英文+中文）：")
        print(f"输入结果1: {result1}")
        
        # 测试2：中文输入
        print("\n测试2：中文输入测试")
        result2 = display.get_text_input("请输入中文：你好世界")
        print(f"输入结果2: {result2}")
        
        # 测试3：长文本输入
        print("\n测试3：长文本输入")
        result3 = display.get_text_input("请输入一段较长的文字：")
        print(f"输入结果3: {result3}")
        
        # 关闭显示
        display.close()
        print("\n✓ 所有输入测试完成")
        
        # 检查结果
        if result1 is not None or result2 is not None or result3 is not None:
            print("✓ 输入功能正常工作")
            return True
        else:
            print("⚠ 所有输入都被取消，请检查功能")
            return False
        
    except Exception as e:
        print(f"✗ 输入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_experiment_input():
    """测试实验流程中的输入"""
    print("\n" + "=" * 60)
    print("测试实验流程中的输入功能")
    print("=" * 60)
    
    try:
        from experiment_flow import CuriosityExperiment
        
        print("创建实验对象...")
        experiment = CuriosityExperiment("test_input", use_eyelink=False, fullscreen=False)
        
        print("✓ 实验对象创建成功")
        
        # 初始化组件
        if experiment.initialize_components():
            print("✓ 实验组件初始化成功")
            
            if experiment.display.dummy_mode:
                print("⚠ 当前为模拟模式，跳过真实输入测试")
                experiment.cleanup()
                return True
            
            # 模拟一个简化的试次来测试输入
            print("\n开始模拟试次输入测试...")
            print("请在弹出的窗口中输入答案并按回车确认")
            
            # 模拟题目数据
            question_data = {
                'id': 1,
                'question': '测试题目：1+1等于几？',
                'answer': '2'
            }
            
            # 测试输入部分
            print("显示题目...")
            experiment.display.show_question(question_data['question'], 3)
            
            print("开始输入测试...")
            start_time = time.time()
            participant_response = experiment.display.get_text_input("请输入您的答案：")
            input_duration = time.time() - start_time
            
            print(f"输入结果: {participant_response}")
            print(f"输入用时: {input_duration:.2f}秒")
            
            if participant_response:
                print("✓ 实验流程输入测试成功")
                result = True
            else:
                print("⚠ 输入被取消或为空")
                result = False
        else:
            print("✗ 实验组件初始化失败")
            result = False
        
        # 清理资源
        experiment.cleanup()
        return result
        
    except Exception as e:
        print(f"✗ 实验输入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chinese_input_specifically():
    """专门测试中文输入"""
    print("\n" + "=" * 60)
    print("专门测试中文输入功能")
    print("=" * 60)
    
    try:
        from experiment_display import ExperimentDisplay
        
        display = ExperimentDisplay(fullscreen=False)
        
        if display.dummy_mode:
            print("⚠ 模拟模式，无法测试真实中文输入")
            return True
        
        print("请测试以下中文输入场景：")
        print("1. 简单中文词汇")
        print("2. 中英文混合")
        print("3. 标点符号")
        print("4. 数字和中文")
        
        test_cases = [
            "请输入：你好",
            "请输入：Hello世界",
            "请输入：测试，标点！",
            "请输入：123中文456"
        ]
        
        results = []
        for i, prompt in enumerate(test_cases, 1):
            print(f"\n测试{i}: {prompt}")
            result = display.get_text_input(prompt)
            results.append(result)
            print(f"结果{i}: {result}")
        
        display.close()
        
        # 检查是否有成功的输入
        success_count = sum(1 for r in results if r is not None and r.strip())
        print(f"\n成功输入: {success_count}/{len(test_cases)}")
        
        if success_count > 0:
            print("✓ 中文输入功能基本正常")
            return True
        else:
            print("⚠ 中文输入可能存在问题")
            return False
        
    except Exception as e:
        print(f"✗ 中文输入测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("输入功能改进测试")
    print("=" * 80)
    
    import time
    
    tests = [
        ("基本文本输入功能", test_text_input),
        ("中文输入专项测试", test_chinese_input_specifically),
        ("实验流程输入测试", test_experiment_input)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
            print(f"测试结果: {'✓ 通过' if success else '✗ 失败'}")
        except Exception as e:
            print(f"✗ {test_name} 测试出现异常: {e}")
            results.append((test_name, False))
        
        # 测试间隔
        time.sleep(1)
    
    # 显示测试结果
    print("\n" + "=" * 80)
    print("输入功能测试结果汇总")
    print("=" * 80)
    
    passed = 0
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有输入功能测试通过！")
        print("\n改进功能说明：")
        print("✓ 支持中文输入法")
        print("✓ 支持连续字符输入")
        print("✓ 按回车键确认，无时间限制")
        print("✓ 按ESC键取消输入")
        print("✓ 自动回退到兼容模式")
    else:
        print("⚠ 部分测试失败，但基本功能应该可用")
        print("注意：在没有PsychoPy的环境中会自动使用模拟模式")

if __name__ == "__main__":
    main()
