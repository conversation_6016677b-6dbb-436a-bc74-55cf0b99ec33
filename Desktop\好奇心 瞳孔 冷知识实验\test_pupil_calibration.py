#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
瞳孔校准程序测试脚本
测试校准程序的各项功能
"""

import os
import sys
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_calibration_system_initialization():
    """测试校准系统初始化"""
    print("=" * 60)
    print("测试校准系统初始化")
    print("=" * 60)
    
    try:
        from pupil_calibration import PupilCalibrationSystem
        
        print("创建校准系统...")
        calibration_system = PupilCalibrationSystem(
            participant_id="test_001",
            fullscreen=False,  # 测试时使用窗口模式
            dummy_mode=True    # 使用虚拟模式测试
        )
        
        print("✓ 校准系统创建成功")
        
        # 测试初始化
        if calibration_system.initialize_system():
            print("✓ 系统初始化成功")
            
            # 检查关键组件
            print(f"✓ 显示就绪: {calibration_system.display_ready}")
            print(f"✓ EyeLink连接: {calibration_system.is_connected}")
            print(f"✓ 校准点数量: {len(calibration_system.calibration_points)}")
            print(f"✓ 验证点数量: {len(calibration_system.validation_points)}")
            print(f"✓ 四角点数量: {len(calibration_system.corner_points)}")
            
            # 清理资源
            calibration_system.cleanup()
            return True
        else:
            print("✗ 系统初始化失败")
            return False
        
    except Exception as e:
        print(f"✗ 校准系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_calibration_points_setup():
    """测试校准点设置"""
    print("\n" + "=" * 60)
    print("测试校准点设置")
    print("=" * 60)
    
    try:
        from pupil_calibration import PupilCalibrationSystem
        
        calibration_system = PupilCalibrationSystem(
            participant_id="test_points",
            screen_size=(1920, 1080),
            dummy_mode=True
        )
        
        calibration_system._setup_calibration_points()
        
        print("校准点位置:")
        for i, (x, y) in enumerate(calibration_system.calibration_points, 1):
            print(f"  点 {i}: ({x:.0f}, {y:.0f})")
        
        print("\n验证点位置:")
        for i, (x, y) in enumerate(calibration_system.validation_points, 1):
            print(f"  点 {i}: ({x:.0f}, {y:.0f})")
        
        print("\n四角点位置:")
        for i, (x, y) in enumerate(calibration_system.corner_points, 1):
            print(f"  角 {i}: ({x:.0f}, {y:.0f})")
        
        # 验证点位置合理性
        w, h = calibration_system.screen_size
        for x, y in calibration_system.calibration_points:
            if not (0 <= x <= w and 0 <= y <= h):
                print(f"✗ 校准点位置超出屏幕范围: ({x}, {y})")
                return False
        
        print("✓ 所有校准点位置设置正确")
        return True
        
    except Exception as e:
        print(f"✗ 校准点设置测试失败: {e}")
        return False

def test_dummy_mode_functionality():
    """测试虚拟模式功能"""
    print("\n" + "=" * 60)
    print("测试虚拟模式功能")
    print("=" * 60)
    
    try:
        from pupil_calibration import PupilCalibrationSystem
        
        calibration_system = PupilCalibrationSystem(
            participant_id="test_dummy",
            fullscreen=False,
            dummy_mode=True
        )
        
        if not calibration_system.initialize_system():
            print("✗ 虚拟模式初始化失败")
            return False
        
        print("✓ 虚拟模式初始化成功")
        
        # 测试校准
        print("测试虚拟校准...")
        if calibration_system.run_calibration():
            print("✓ 虚拟校准成功")
        else:
            print("✗ 虚拟校准失败")
            return False
        
        # 测试眼动数据获取
        print("测试虚拟眼动数据...")
        gaze_pos = calibration_system._get_current_gaze()
        if gaze_pos:
            print(f"✓ 虚拟眼动数据: ({gaze_pos[0]:.1f}, {gaze_pos[1]:.1f})")
        else:
            print("⚠ 无虚拟眼动数据")
        
        # 清理资源
        calibration_system.cleanup()
        print("✓ 虚拟模式测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 虚拟模式测试失败: {e}")
        return False

def test_real_mode_initialization():
    """测试真实模式初始化（不连接真实设备）"""
    print("\n" + "=" * 60)
    print("测试真实模式初始化")
    print("=" * 60)
    
    try:
        from pupil_calibration import PupilCalibrationSystem
        
        print("尝试真实模式初始化（预期会回退到虚拟模式）...")
        calibration_system = PupilCalibrationSystem(
            participant_id="test_real",
            fullscreen=False,
            dummy_mode=False  # 尝试真实模式
        )
        
        if calibration_system.initialize_system():
            print("✓ 系统初始化成功")
            print(f"✓ 最终模式: {'虚拟模式' if calibration_system.dummy_mode else '真实模式'}")
            
            # 清理资源
            calibration_system.cleanup()
            return True
        else:
            print("✗ 系统初始化失败")
            return False
        
    except Exception as e:
        print(f"✗ 真实模式测试失败: {e}")
        return False

def test_coordinate_conversion():
    """测试坐标转换"""
    print("\n" + "=" * 60)
    print("测试坐标转换")
    print("=" * 60)
    
    try:
        # 测试屏幕坐标到PsychoPy坐标的转换
        screen_size = (1920, 1080)
        
        test_points = [
            (0, 0),           # 左上角
            (1920, 0),        # 右上角
            (0, 1080),        # 左下角
            (1920, 1080),     # 右下角
            (960, 540),       # 中心
        ]
        
        print("屏幕坐标 → PsychoPy坐标转换:")
        for screen_x, screen_y in test_points:
            # 转换公式
            psychopy_x = screen_x - screen_size[0]/2
            psychopy_y = screen_size[1]/2 - screen_y
            
            print(f"  ({screen_x:4.0f}, {screen_y:4.0f}) → ({psychopy_x:6.1f}, {psychopy_y:6.1f})")
        
        print("✓ 坐标转换测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 坐标转换测试失败: {e}")
        return False

def test_program_structure():
    """测试程序结构完整性"""
    print("\n" + "=" * 60)
    print("测试程序结构完整性")
    print("=" * 60)
    
    try:
        from pupil_calibration import PupilCalibrationSystem
        
        # 检查关键方法是否存在
        required_methods = [
            'initialize_system',
            'run_calibration',
            'start_real_time_display',
            '_get_current_gaze',
            '_update_display',
            '_draw_corner_points',
            'cleanup'
        ]
        
        for method_name in required_methods:
            if hasattr(PupilCalibrationSystem, method_name):
                print(f"✓ 方法存在: {method_name}")
            else:
                print(f"✗ 方法缺失: {method_name}")
                return False
        
        print("✓ 程序结构完整")
        return True
        
    except Exception as e:
        print(f"✗ 程序结构测试失败: {e}")
        return False

def run_interactive_demo():
    """运行交互式演示"""
    print("\n" + "=" * 60)
    print("交互式演示")
    print("=" * 60)
    
    try:
        from pupil_calibration import PupilCalibrationSystem
        
        print("创建演示系统...")
        demo_system = PupilCalibrationSystem(
            participant_id="demo",
            fullscreen=False,
            dummy_mode=True
        )
        
        if not demo_system.initialize_system():
            print("演示系统初始化失败")
            return False
        
        print("✓ 演示系统就绪")
        print("\n演示功能:")
        print("1. 校准演示")
        print("2. 实时显示演示（简化版）")
        
        choice = input("\n选择演示功能 (1/2, 默认1): ").strip()
        
        if choice == "2":
            print("开始实时显示演示...")
            demo_system._simulate_real_time_display()
        else:
            print("开始校准演示...")
            demo_system.run_calibration()
        
        demo_system.cleanup()
        print("✓ 演示完成")
        return True
        
    except Exception as e:
        print(f"✗ 交互式演示失败: {e}")
        return False

def main():
    """主测试函数"""
    print("瞳孔校准程序测试套件")
    print("=" * 80)
    
    tests = [
        ("校准系统初始化", test_calibration_system_initialization),
        ("校准点设置", test_calibration_points_setup),
        ("虚拟模式功能", test_dummy_mode_functionality),
        ("真实模式初始化", test_real_mode_initialization),
        ("坐标转换", test_coordinate_conversion),
        ("程序结构完整性", test_program_structure)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
            print(f"测试结果: {'✓ 通过' if success else '✗ 失败'}")
        except Exception as e:
            print(f"✗ {test_name} 测试出现异常: {e}")
            results.append((test_name, False))
        
        time.sleep(0.5)
    
    # 显示测试结果
    print("\n" + "=" * 80)
    print("瞳孔校准程序测试结果汇总")
    print("=" * 80)
    
    passed = 0
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！校准程序功能正常")
        
        # 询问是否运行演示
        demo_choice = input("\n是否运行交互式演示? (y/n): ").strip().lower()
        if demo_choice == 'y':
            run_interactive_demo()
    else:
        print("⚠ 部分测试失败，请检查程序")
    
    print("\n使用说明:")
    print("1. 运行校准程序: python pupil_calibration.py")
    print("2. 查看校准原理: 阅读 EyeLink校准原理详解.md")
    print("3. 在真实环境中需要连接EyeLink设备")

if __name__ == "__main__":
    main()
