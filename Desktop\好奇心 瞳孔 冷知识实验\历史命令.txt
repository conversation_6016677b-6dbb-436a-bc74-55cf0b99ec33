我要用psycopy写一些python代码。所有内容在conda 中eyetracking的环境进行。我用eyelink的眼动仪。我会让被试在实验材料库中选择一些题目做随机选3个先，同时记录他的瞳孔和眼动数据。实验流程在单独的txt文档中。现在按照我的实验流程编写一个python程序，有3道随机的题目。写一个todo一个个完成
改进以下内容：1.所有数据存在data文件夹下，命名用日期+时间+id。 2.你需要参考eyelink python example中的pylink api user guide。你需要让eyelink从开始实验后记录瞳孔、眼动、事件数据，并且在结束后返回一个edf文件，具体的事件数据描述当时发生什么，例如“出示题目“1+1=几””，“眼动校准1”，“出示答案”等等。
详细的向我介绍你的每一个python程序的作用 以及其中可以调节的参数。然后修改以下问题：在填空输入的时候，我输入中文每次只能输入一个字，修改成一次可以输入多个。2，填空后只有按回车才换到下一个环节，不是限定时间的。列一个todo一个个完成。
1debug,还是不能连续输入中文（获取文本输入失败: 'TextBox2' object has no attribute 'refresh'）,请仍然用这种对话框，上网搜一下如何键入多个中文；2，改成默认全屏并且使用eyelink机器，默认一道题