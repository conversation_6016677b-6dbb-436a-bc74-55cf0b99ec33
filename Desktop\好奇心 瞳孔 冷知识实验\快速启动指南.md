# 好奇心瞳孔冷知识实验 - 快速启动指南

## 🚀 快速开始

### 1. 环境检查
确保您在正确的conda环境中：
```bash
conda activate eyetracking
```

### 2. 快速测试
运行测试脚本验证所有功能：
```bash
python test_improvements.py
```

### 3. 启动实验
双击 `run_experiment.bat` 或运行：
```bash
python main_experiment.py
```

## 📊 数据位置

所有实验数据保存在：
```
data/
└── [日期]_[时间]_[被试ID]/
    ├── [被试ID].edf          # EyeLink眼动数据
    ├── experiment_data.csv    # 分析用CSV数据
    ├── experiment_log.txt     # 详细日志
    └── ...其他文件
```

## 🔧 常见问题

### Q: EyeLink连接失败怎么办？
A: 程序会自动切换到虚拟模式，仍可进行实验流程测试。

### Q: PsychoPy无法启动怎么办？
A: 程序会自动切换到模拟模式，在控制台显示实验进度。

### Q: 如何查看实验数据？
A: 
- CSV文件可用Excel打开
- EDF文件需要EyeLink Data Viewer
- JSON文件包含详细的试次数据

## 📝 实验流程提醒

1. **实验前**：
   - 检查EyeLink连接
   - 调整被试座椅位置
   - 确认实验室光线条件
   - 测试中文输入功能

2. **实验中**：
   - 提醒被试保持头部稳定
   - 观察眼动数据质量
   - 注意被试状态
   - **输入阶段**：告知被试输入完成后按回车确认

3. **实验后**：
   - 检查数据完整性
   - 备份重要数据
   - 记录实验备注

## 🆕 输入功能说明

### 答案输入操作
- ✅ **支持中文输入**：可以使用任何中文输入法
- ✅ **无时间限制**：被试可以充分思考和输入
- ✅ **回车确认**：输入完成后按回车键进入下一阶段
- ✅ **ESC取消**：如需取消可按ESC键
- ✅ **退格删除**：支持正常的编辑操作

### 被试操作指导
1. 看到输入框后，直接开始输入答案
2. 可以输入中文、英文、数字、标点符号
3. 输入错误可以用退格键删除
4. 输入完成后按回车键确认
5. 如需跳过可按ESC键

## 🆘 技术支持

如遇问题，请检查：
1. `experiment_log.txt` - 详细错误日志
2. 控制台输出 - 实时错误信息
3. `data/` 目录 - 数据保存情况

## 📞 联系方式

如需技术支持，请提供：
- 错误截图
- 日志文件
- 实验环境信息
