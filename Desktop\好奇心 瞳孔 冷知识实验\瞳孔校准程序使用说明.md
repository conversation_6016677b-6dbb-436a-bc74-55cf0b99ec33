# 瞳孔校准程序使用说明

## 📋 程序概述

这是一个专门的EyeLink瞳孔校准程序，具有以下功能：
- **EyeLink校准**：标准9点校准流程
- **实时眼动显示**：显示当前注视点位置
- **四角验证**：在屏幕四角显示标定点验证精度
- **智能模式切换**：自动在真实模式和虚拟模式间切换

## 🚀 快速开始

### 1. 运行程序
```bash
# 激活conda环境
conda activate eyetracking

# 运行校准程序
python pupil_calibration.py
```

### 2. 测试程序
```bash
# 运行测试套件
python test_pupil_calibration.py
```

## 🔧 程序功能详解

### 主要功能模块

#### 1. 校准系统初始化
- **EyeLink连接**：自动连接到EyeLink主机（IP: *********）
- **显示系统**：创建全屏PsychoPy窗口
- **参数配置**：设置1000Hz采样率和数据记录格式

#### 2. 标准校准流程
```
校准点布局（9点校准）：
1 ---- 2 ---- 3
|      |      |
4 ---- 5 ---- 6
|      |      |
7 ---- 8 ---- 9
```

**校准步骤**：
1. 调用`doTrackerSetup()`进入EyeLink校准界面
2. 按照提示完成9点校准
3. 进行验证确认精度
4. 返回程序继续

#### 3. 实时眼动显示
- **红色光标**：显示当前注视点位置
- **黄色轨迹**：显示最近20个注视点的轨迹
- **绿色角点**：四个角落的标定点
- **信息面板**：显示当前状态和操作说明

#### 4. 四角验证功能
按`C`键激活四角验证：
- 依次高亮四个角落的标定点
- 被试注视高亮点，观察注视光标是否准确
- 用于验证校准精度

## 🎮 操作说明

### 键盘控制
- **ESC键**：退出程序
- **SPACE键**：重新校准
- **C键**：显示四角验证

### 鼠标控制（虚拟模式）
- 在虚拟模式下，鼠标位置模拟眼动数据
- 移动鼠标可以看到注视光标跟随

## 📊 校准质量评估

### 精度指标
- **优秀**：平均误差 < 0.5°，最大误差 < 1.0°
- **可接受**：平均误差 < 1.0°，最大误差 < 2.0°
- **需重校准**：误差超过可接受范围

### 验证方法
1. **四角验证**：注视四个角落，观察光标位置
2. **中心验证**：注视屏幕中心，检查光标准确性
3. **边缘验证**：注视屏幕边缘，测试覆盖范围

## 🔍 故障排除

### 常见问题

#### 1. EyeLink连接失败
**现象**：程序显示"EyeLink连接失败"
**解决方案**：
- 检查EyeLink主机是否开启
- 确认网络连接（IP: *********）
- 检查防火墙设置
- 程序会自动切换到虚拟模式继续运行

#### 2. PsychoPy显示问题
**现象**：窗口无法创建或显示异常
**解决方案**：
- 更新显卡驱动
- 关闭其他占用显卡的程序
- 尝试非全屏模式
- 程序会自动切换到模拟模式

#### 3. 校准精度不佳
**现象**：注视光标位置不准确
**解决方案**：
- 调整被试座椅高度
- 确保头部稳定（使用头托）
- 检查光照条件
- 重新运行校准（按SPACE键）

#### 4. 数据记录问题
**现象**：无法保存EDF文件
**解决方案**：
- 检查磁盘空间
- 确认写入权限
- 检查EyeLink连接状态

## 📁 输出文件

### 数据文件
- **EDF文件**：`{participant_id}_calibration.edf`
- **包含数据**：
  - 校准参数
  - 验证结果
  - 实时眼动数据
  - 事件标记

### 文件位置
所有输出文件保存在程序运行目录下。

## ⚙️ 高级配置

### 修改校准参数
在`pupil_calibration.py`中可以调整：

```python
# 屏幕尺寸
screen_size = (1920, 1080)

# 采样率
self.tracker.sendCommand("sample_rate 1000")

# 校准点位置
self.calibration_points = [...]  # 自定义校准点
```

### 自定义校准点
```python
# 5点校准（快速模式）
calibration_points = [
    (w*0.1, h*0.1),   # 左上
    (w*0.9, h*0.1),   # 右上
    (w*0.5, h*0.5),   # 中心
    (w*0.1, h*0.9),   # 左下
    (w*0.9, h*0.9),   # 右下
]
```

## 🔬 技术原理

### EyeLink工作原理
1. **红外光照射**：照射眼球
2. **瞳孔检测**：识别瞳孔中心
3. **角膜反射**：检测角膜反射点
4. **相对位置**：计算两者相对位置
5. **坐标映射**：映射到屏幕坐标

### 校准数学模型
```python
# 二次多项式映射
screen_x = a0 + a1*px + a2*py + a3*px*py + a4*px² + a5*py²
screen_y = b0 + b1*px + b2*py + b3*px*py + b4*px² + b5*py²
```

## 📞 技术支持

### 调试信息
程序运行时会输出详细的状态信息：
- 连接状态
- 校准结果
- 数据质量
- 错误信息

### 日志记录
所有重要事件都会记录在控制台输出中，便于问题诊断。

## 🎯 最佳实践

### 实验前准备
1. **环境检查**：确保光照适中，无强光直射
2. **设备检查**：EyeLink主机正常，网络连接稳定
3. **被试准备**：调整座椅，设置头托
4. **程序测试**：先运行测试确认功能正常

### 校准过程
1. **说明重要性**：向被试解释校准的重要性
2. **练习注视**：让被试练习稳定注视
3. **逐点校准**：耐心完成每个校准点
4. **验证确认**：使用四角验证检查精度

### 实验中维护
1. **定期检查**：监控数据质量
2. **适时重校准**：长时间实验需要重新校准
3. **记录问题**：记录任何异常情况

这个校准程序为眼动实验提供了专业、可靠的校准工具，确保高质量的眼动数据采集。
