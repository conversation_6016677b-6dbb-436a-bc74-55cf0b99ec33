# 好奇心瞳孔冷知识实验 - 程序功能详解

## 📋 程序架构总览

本实验程序采用模块化设计，共包含6个核心Python程序，每个程序负责特定的功能：

```
实验程序架构
├── main_experiment.py          # 主控制器
├── experiment_flow.py          # 流程控制
├── experiment_display.py       # 界面显示
├── experiment_materials.py     # 材料管理
├── eyelink_manager.py          # 眼动仪控制
└── data_manager.py             # 数据管理
```

## 🔧 各程序详细功能和参数

### 1. `main_experiment.py` - 主实验程序
**功能**：实验启动器和总控制器，负责用户交互和整体协调

**可调节参数**：
```python
# 显示设置
fullscreen = False              # 是否全屏显示
screen_size = (1920, 1080)     # 屏幕分辨率

# 实验设置
use_eyelink = True              # 是否使用EyeLink
num_questions = 3               # 题目数量（1-10）

# 被试信息
participant_id = "001"          # 被试ID
```

**主要功能**：
- 依赖检查和环境验证
- 被试信息收集
- 实验参数配置
- 错误处理和资源清理

---

### 2. `experiment_flow.py` - 实验流程控制
**功能**：控制整个实验的执行流程，协调各个组件

**可调节参数**：
```python
# 时间控制参数
self.timing = {
    'fixation_baseline': random.uniform(5, 7),  # 基线校准时间 5-7秒
    'question_display': 8.0,                   # 问题显示时间 8秒
    'answer_input': None,                      # 答案输入（无时间限制）
    'curiosity_rating': 10.0,                  # 好奇心评分时间 10秒
    'pupil_baseline': 3.0,                     # 瞳孔基线时间 3秒
    'answer_display': 10.0,                    # 答案显示时间 10秒
    'pleasure_rating': 10.0,                   # 愉悦度评分时间 10秒
    'surprise_rating': 10.0                    # 意外程度评分时间 10秒
}

# 评分标签
self.curiosity_labels = ["完全不想知道", "稍有兴趣", "可以知道一下", "比较感兴趣", "极其好奇"]
self.pleasure_labels = ["太无聊了", "有点无聊", "一般般", "有点意思", "太有趣了"]
self.surprise_labels = ["不意外", "有些意外", "很意外"]
```

**主要功能**：
- 实验流程编排
- 试次管理
- 数据收集协调
- 组件间通信

---

### 3. `experiment_display.py` - 实验界面显示
**功能**：管理所有视觉界面的显示，包括刺激呈现和用户交互

**可调节参数**：
```python
# 窗口设置
screen_size = (1920, 1080)      # 屏幕尺寸
fullscreen = False              # 全屏模式

# 文字显示参数
font = 'SimHei'                 # 中文字体
text_height = 40                # 文字大小
text_color = 'white'            # 文字颜色
wrap_width = 1600               # 文字换行宽度

# 十字准星参数
fixation_size = 20              # 十字准星大小
fixation_color = 'white'        # 十字准星颜色
fixation_width = 3              # 线条宽度

# 输入框参数
input_box_size = (800, 60)      # 输入框大小
input_font_size = 25            # 输入字体大小
input_bg_color = 'white'        # 输入框背景色
input_border_color = 'gray'     # 输入框边框色

# 评分界面参数
rating_spacing = 800            # 评分选项间距
rating_font_size = 40           # 评分数字大小
```

**主要功能**：
- 十字准星显示
- 题目文字呈现
- 🆕 中文输入支持（TextBox2）
- 评分界面
- 答案显示

---

### 4. `experiment_materials.py` - 实验材料管理
**功能**：加载、管理和随机选择实验题目

**可调节参数**：
```python
# 材料文件设置
materials_file = "实验材料库.txt"  # 材料文件路径

# 题目选择参数
num_questions = 3               # 随机选择题目数量
min_question_length = 10        # 最短题目长度（字符）
max_questions_total = 100       # 最大题目总数

# 题目过滤参数
filter_short_questions = True   # 是否过滤过短题目
filter_duplicates = True        # 是否过滤重复题目
```

**主要功能**：
- 题目文件解析
- 题目答案匹配
- 随机选择算法
- 题目质量过滤

---

### 5. `eyelink_manager.py` - EyeLink眼动仪管理
**功能**：控制EyeLink眼动仪的所有功能，基于官方API指南

**可调节参数**：
```python
# 硬件设置
screen_width = 1920             # 屏幕宽度
screen_height = 1080            # 屏幕高度
host_ip = "*********"          # EyeLink主机IP

# 采样设置
sample_rate = 1000              # 采样率（Hz）
pupil_diameter = True           # 瞳孔直径测量

# 事件检测参数
saccade_velocity_threshold = 30         # 眼跳速度阈值
saccade_acceleration_threshold = 9500   # 眼跳加速度阈值
saccade_motion_threshold = 0.15         # 眼跳运动阈值

# 数据记录设置
file_event_filter = "LEFT,RIGHT,FIXATION,SACCADE,BLINK,MESSAGE,BUTTON,INPUT"
file_sample_data = "LEFT,RIGHT,GAZE,HREF,RAW,AREA,GAZERES,BUTTON,STATUS,INPUT"
```

**主要功能**：
- EyeLink连接管理
- 校准控制
- 数据记录控制
- 🆕 详细事件标记
- EDF文件接收

---

### 6. `data_manager.py` - 数据管理
**功能**：处理所有数据的保存、管理和验证

**可调节参数**：
```python
# 目录设置
base_data_dir = "data"          # 基础数据目录
backup_enabled = True           # 是否启用自动备份

# 文件命名格式
timestamp_format = "%Y%m%d_%H%M%S"  # 时间戳格式
dir_name_format = "{timestamp}_{participant_id}"  # 目录命名格式

# 数据保存格式
save_json = True                # 保存JSON格式
save_csv = True                 # 保存CSV格式
save_log = True                 # 保存日志文件

# 数据验证设置
validate_required_fields = True  # 验证必需字段
validate_data_types = True      # 验证数据类型
```

**主要功能**：
- 🆕 统一数据目录管理
- 多格式数据保存
- 数据完整性验证
- 自动备份功能

## 🆕 最新改进功能

### 1. 中文输入支持
- **问题**：原来每次只能输入一个中文字符
- **解决方案**：使用PsychoPy的TextBox2组件
- **效果**：完全支持中文输入法，可连续输入

### 2. 无时间限制输入
- **问题**：原来输入有15秒时间限制
- **解决方案**：移除时间限制，改为按回车确认
- **效果**：被试可以充分思考和输入答案

### 3. 智能回退机制
- **功能**：当TextBox2不可用时自动回退到兼容模式
- **好处**：确保程序在各种环境下都能运行

## 🎛️ 参数调节指南

### 时间参数调节
在 `experiment_flow.py` 中修改 `self.timing` 字典：
```python
# 延长题目显示时间到12秒
'question_display': 12.0,

# 缩短评分时间到8秒
'curiosity_rating': 8.0,
```

### 界面参数调节
在 `experiment_display.py` 中修改相关参数：
```python
# 增大文字大小
height=50,

# 改变文字颜色
color='lightblue',

# 调整输入框大小
size=(1000, 80),
```

### EyeLink参数调节
在 `eyelink_manager.py` 中修改配置：
```python
# 提高采样率
self.tracker.sendCommand("sample_rate 2000")

# 调整眼跳检测阈值
self.tracker.sendCommand("saccade_velocity_threshold = 35")
```

## 🔧 自定义扩展

### 添加新的评分类型
1. 在 `experiment_flow.py` 中添加新的评分标签
2. 在实验流程中添加新的评分步骤
3. 更新数据记录结构

### 修改实验流程
1. 在 `run_single_trial` 方法中调整步骤顺序
2. 添加或删除实验阶段
3. 更新EyeLink事件标记

### 扩展数据分析
1. 在 `data_manager.py` 中添加新的分析方法
2. 扩展CSV输出格式
3. 添加实时数据可视化

这个程序架构具有高度的可配置性和扩展性，可以根据具体的实验需求进行灵活调整。
